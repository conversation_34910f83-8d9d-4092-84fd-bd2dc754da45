#!/usr/bin/env python3
"""
Quick test to verify MAML functionality works with new Transformer models.
"""

import torch
import numpy as np
from src.agents.moe_agent import MoEAgent
from src.training.trainer import Trainer

def test_maml_adaptation():
    """Test that MAML adaptation works with Transformer-based experts."""
    print("Testing MAML adaptation with Transformer-based experts...")
    
    expert_configs = {
        "TrendAgent": {"hidden_dim": 64},
        "MeanReversionAgent": {"hidden_dim": 64},
        "VolatilityAgent": {"hidden_dim": 64}
    }
    
    agent = MoEAgent(
        observation_dim=50,
        action_dim=3,
        hidden_dim=64,
        expert_configs=expert_configs
    )
    
    print("✓ MoE agent instantiated successfully")
    
    # Test adaptation (MAML inner loop)
    observation = np.random.randn(50)
    action = 1
    reward = 0.5
    next_observation = np.random.randn(50)
    done = False
    num_gradient_steps = 3
    
    print("Testing MAML adaptation...")
    adapted_agent = agent.adapt(observation, action, reward, next_observation, done, num_gradient_steps)
    
    print("✓ MAML adaptation completed successfully")
    
    # Verify adapted agent is different instance but same type
    assert isinstance(adapted_agent, MoEAgent), f"Adapted agent is {type(adapted_agent)}"
    assert len(adapted_agent.experts) == len(agent.experts), "Adapted agent should have same number of experts"
    
    # Test that adapted agent can select actions
    adapted_action = adapted_agent.select_action(observation)
    print(f"✓ Adapted agent action selection works: {adapted_action}")
    
    # Test that expert models are still Transformer types
    from src.models.transformer_models import ActorTransformerModel, CriticTransformerModel
    
    for i, expert in enumerate(adapted_agent.experts):
        assert isinstance(expert.actor, ActorTransformerModel), f"Adapted expert {i} actor is {type(expert.actor)}"
        assert isinstance(expert.critic, CriticTransformerModel), f"Adapted expert {i} critic is {type(expert.critic)}"
        assert isinstance(expert.policy_old, ActorTransformerModel), f"Adapted expert {i} policy_old is {type(expert.policy_old)}"
    
    print("✓ All adapted expert models are correct Transformer types")
    
    # Test meta-optimizer creation
    trainer = Trainer(agent, num_episodes=10, meta_lr=0.001)
    assert trainer.meta_optimizer is not None, "Meta-optimizer should be created for MoE agent"
    
    print("✓ Meta-optimizer created successfully")
    
    # Test that meta-optimizer has parameters from all experts
    meta_param_count = sum(p.numel() for p in trainer.meta_optimizer.param_groups[0]['params'])
    print(f"✓ Meta-optimizer has {meta_param_count} parameters")
    
    # Verify gating network parameters are included
    gating_param_count = sum(p.numel() for p in agent.gating_network.parameters())
    print(f"✓ Gating network has {gating_param_count} parameters")
    
    # Test forward passes with adapted agent
    batch_size = 2
    seq_len = 5
    dummy_states = torch.randn(batch_size, seq_len, 50)
    
    for i, expert in enumerate(adapted_agent.experts):
        actor_output = expert.actor(dummy_states)
        critic_output = expert.critic(dummy_states)
        
        assert actor_output.shape == (batch_size, 3), f"Adapted expert {i} actor output shape: {actor_output.shape}"
        assert critic_output.shape == (batch_size, 1), f"Adapted expert {i} critic output shape: {critic_output.shape}"
    
    print("✓ All adapted expert forward passes work correctly")
    print("✓ All MAML tests passed!")

def test_meta_learning_components():
    """Test that meta-learning components are properly set up."""
    print("\nTesting meta-learning components...")
    
    expert_configs = {
        "TrendAgent": {"hidden_dim": 32},
        "MeanReversionAgent": {"hidden_dim": 32}
    }
    
    agent = MoEAgent(
        observation_dim=20,
        action_dim=2,
        hidden_dim=32,
        expert_configs=expert_configs
    )
    
    trainer = Trainer(agent, num_episodes=5, meta_lr=0.001)
    
    # Test that trainer recognizes MoE agent for meta-learning
    assert isinstance(trainer.agent, MoEAgent), "Trainer should have MoE agent"
    assert trainer.meta_optimizer is not None, "Meta-optimizer should be created"
    
    print("✓ Meta-learning components properly initialized")
    
    # Test that all expert parameters are included in meta-optimizer
    expert_params = []
    for expert in agent.experts:
        expert_params.extend(list(expert.actor.parameters()))
        expert_params.extend(list(expert.critic.parameters()))
    
    gating_params = list(agent.gating_network.parameters())
    
    total_expected_params = len(expert_params) + len(gating_params)
    actual_params = len(trainer.meta_optimizer.param_groups[0]['params'])
    
    print(f"✓ Expected {total_expected_params} parameters, got {actual_params}")
    
    print("✓ All meta-learning component tests passed!")

if __name__ == "__main__":
    test_maml_adaptation()
    test_meta_learning_components()
    print("\n🎉 All MAML Transformer tests passed successfully!")
