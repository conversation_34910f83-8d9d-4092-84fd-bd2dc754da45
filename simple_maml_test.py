#!/usr/bin/env python3
"""
Simple test for MAML with Transformers.
"""

import numpy as np
from src.agents.moe_agent import MoEAgent

def test_simple_maml():
    print("Testing MAML adaptation...")
    
    expert_configs = {
        "TrendAgent": {"hidden_dim": 32}
    }
    
    agent = MoEAgent(
        observation_dim=10,
        action_dim=2,
        hidden_dim=32,
        expert_configs=expert_configs
    )
    
    print("✓ MoE agent created")
    
    # Test adaptation
    observation = np.random.randn(10)
    adapted_agent = agent.adapt(observation, 1, 0.5, observation, False, 1)
    
    print("✓ MAML adaptation successful")
    print("✓ Test passed!")

if __name__ == "__main__":
    test_simple_maml()
