["tests/test_backtesting/test_engine.py::test_buy_long_option", "tests/test_backtesting/test_engine.py::test_buy_long_stock", "tests/test_backtesting/test_engine.py::test_close_long_option", "tests/test_backtesting/test_engine.py::test_close_long_stock", "tests/test_backtesting/test_engine.py::test_initial_capital_validation", "tests/test_backtesting/test_engine.py::test_insufficient_capital_buy", "tests/test_backtesting/test_engine.py::test_reset", "tests/test_backtesting/test_engine.py::test_trailing_stop_long_position", "tests/test_backtesting/test_engine.py::test_unrealized_pnl_option", "tests/test_backtesting/test_engine.py::test_unrealized_pnl_stock", "tests/test_config/test_instrument.py::test_instrument_creation", "tests/test_config/test_instrument.py::test_instrument_repr", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_attention_weights", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_config_methods", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_deterministic", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_different_configurations", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_forward_basic", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_gradient_flow", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_initialization", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_with_mask", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_without_positional_encoding", "tests/test_core_transformer.py::TestPositionalEncoding::test_positional_encoding_forward", "tests/test_core_transformer.py::TestPositionalEncoding::test_positional_encoding_initialization", "tests/test_utils/test_instrument_loader.py::test_load_instruments_file_not_found", "tests/test_utils/test_instrument_loader.py::test_load_instruments_invalid_yaml", "tests/test_utils/test_instrument_loader.py::test_load_instruments_success"]