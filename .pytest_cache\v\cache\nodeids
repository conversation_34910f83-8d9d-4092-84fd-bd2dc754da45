["tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_act_basic", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_act_with_memory", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_base_agent_compatibility", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_initialization", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_learn_from_experience", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_memory_capacity", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_save_and_load", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_statistics", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_temperature_exploration", "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_think_and_predict", "tests/test_backtesting/test_engine.py::test_buy_long_option", "tests/test_backtesting/test_engine.py::test_buy_long_stock", "tests/test_backtesting/test_engine.py::test_close_long_option", "tests/test_backtesting/test_engine.py::test_close_long_stock", "tests/test_backtesting/test_engine.py::test_initial_capital_validation", "tests/test_backtesting/test_engine.py::test_insufficient_capital_buy", "tests/test_backtesting/test_engine.py::test_reset", "tests/test_backtesting/test_engine.py::test_trailing_stop_long_position", "tests/test_backtesting/test_engine.py::test_unrealized_pnl_option", "tests/test_backtesting/test_engine.py::test_unrealized_pnl_stock", "tests/test_config/test_instrument.py::test_instrument_creation", "tests/test_config/test_instrument.py::test_instrument_repr", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_attention_weights", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_config_methods", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_deterministic", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_different_configurations", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_forward_basic", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_gradient_flow", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_initialization", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_with_mask", "tests/test_core_transformer.py::TestCoreTransformer::test_core_transformer_without_positional_encoding", "tests/test_core_transformer.py::TestPositionalEncoding::test_positional_encoding_forward", "tests/test_core_transformer.py::TestPositionalEncoding::test_positional_encoding_initialization", "tests/test_episodic_memory.py::TestExternalMemory::test_clear_memory", "tests/test_episodic_memory.py::TestExternalMemory::test_external_memory_initialization", "tests/test_episodic_memory.py::TestExternalMemory::test_importance_decay", "tests/test_episodic_memory.py::TestExternalMemory::test_memory_capacity_and_eviction", "tests/test_episodic_memory.py::TestExternalMemory::test_memory_statistics", "tests/test_episodic_memory.py::TestExternalMemory::test_retrieve_memory_basic", "tests/test_episodic_memory.py::TestExternalMemory::test_retrieve_memory_empty", "tests/test_episodic_memory.py::TestExternalMemory::test_retrieve_memory_with_similarity_threshold", "tests/test_episodic_memory.py::TestExternalMemory::test_retrieve_with_custom_parameters", "tests/test_episodic_memory.py::TestExternalMemory::test_save_and_load_memories", "tests/test_episodic_memory.py::TestExternalMemory::test_store_memory_basic", "tests/test_episodic_memory.py::TestExternalMemory::test_store_memory_dimension_mismatch", "tests/test_episodic_memory.py::TestExternalMemory::test_store_memory_with_torch_tensor", "tests/test_episodic_memory.py::TestMemoryEvent::test_memory_event_creation", "tests/test_market_classifier.py::TestMarketClassifier::test_calculate_adx", "tests/test_market_classifier.py::TestMarketClassifier::test_calculate_atr", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_consolidation_scenario", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_insufficient_data", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_trending_scenario", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_volatile_scenario", "tests/test_market_classifier.py::TestMarketClassifier::test_classify_market_with_confidence", "tests/test_market_classifier.py::TestMarketClassifier::test_error_handling_invalid_data", "tests/test_market_classifier.py::TestMarketClassifier::test_get_market_features", "tests/test_market_classifier.py::TestMarketClassifier::test_get_market_features_insufficient_data", "tests/test_market_classifier.py::TestMarketClassifier::test_get_regime_probabilities", "tests/test_market_classifier.py::TestMarketClassifier::test_get_regime_probabilities_insufficient_data", "tests/test_market_classifier.py::TestMarketClassifier::test_market_classifier_custom_parameters", "tests/test_market_classifier.py::TestMarketClassifier::test_market_classifier_initialization", "tests/test_market_classifier.py::TestMarketClassifier::test_market_regimes_enum", "tests/test_market_classifier.py::TestMarketClassifier::test_prepare_data_dictionary", "tests/test_market_classifier.py::TestMarketClassifier::test_prepare_data_numpy_array", "tests/test_market_classifier.py::TestMarketClassifier::test_prepare_data_pandas_dataframe", "tests/test_models.py::test_actor_transformer_model", "tests/test_models.py::test_critic_transformer_model", "tests/test_models.py::test_transformer_model_multiple_layers", "tests/test_models.py::test_transformer_model_output_shape", "tests/test_nas_framework.py::TestIndividual::test_individual_creation", "tests/test_nas_framework.py::TestNASController::test_build_model_from_best", "tests/test_nas_framework.py::TestNASController::test_crossover", "tests/test_nas_framework.py::TestNASController::test_evaluate_population", "tests/test_nas_framework.py::TestNASController::test_evolve_population", "tests/test_nas_framework.py::TestNASController::test_generate_architecture", "tests/test_nas_framework.py::TestNASController::test_get_best_architecture", "tests/test_nas_framework.py::TestNASController::test_get_statistics", "tests/test_nas_framework.py::TestNASController::test_initialize_population", "tests/test_nas_framework.py::TestNASController::test_mutation", "tests/test_nas_framework.py::TestNASController::test_nas_controller_initialization", "tests/test_nas_framework.py::TestNASController::test_selection", "tests/test_nas_framework.py::TestSearchSpace::test_build_model_from_architecture", "tests/test_nas_framework.py::TestSearchSpace::test_create_layer_from_config", "tests/test_nas_framework.py::TestSearchSpace::test_get_architecture_complexity", "tests/test_nas_framework.py::TestSearchSpace::test_layer_config_creation", "tests/test_nas_framework.py::TestSearchSpace::test_layer_config_validation", "tests/test_nas_framework.py::TestSearchSpace::test_sample_layer_config", "tests/test_nas_framework.py::TestSearchSpace::test_sample_random_architecture", "tests/test_nas_framework.py::TestSearchSpace::test_search_space_initialization", "tests/test_nas_framework.py::TestSearchSpace::test_validate_architecture", "tests/test_utils/test_instrument_loader.py::test_load_instruments_file_not_found", "tests/test_utils/test_instrument_loader.py::test_load_instruments_invalid_yaml", "tests/test_utils/test_instrument_loader.py::test_load_instruments_success", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_config_methods", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_forward_basic", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_gradient_flow", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_initialization", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_interpretability", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_predict_future_states", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_simulate_action_outcomes", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_single_timestep_input", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_with_attention_weights", "tests/test_world_model.py::TestTransformerWorldModel::test_world_model_without_market_regime"]