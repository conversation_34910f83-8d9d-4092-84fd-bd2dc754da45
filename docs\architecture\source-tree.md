# Project Source Tree

This document outlines the standard directory structure for the Autonomous Trading Bot project. Adhering to this structure ensures consistency, maintainability, and ease of navigation for all team members and automated processes.

```
C:/AlgoTrading/
├───.git/                       # Git version control metadata
├───.gemini/                    # Gemini-specific configuration and memory
├───.bmad-core/                 # BMad-Method core agent definitions, tasks, templates, etc.
├───data/
│   ├───raw/                    # Raw, unprocessed historical market data (CSV files)
│   └───final/                  # Final, training-ready datasets (features)
├───docs/
│   ├───mainIdea.md             # High-level project vision and technical specification
│   ├───architecture/           # Architectural documentation
│   │   ├───coding-standards.md # Project-wide coding guidelines
│   │   ├───tech-stack.md       # Overview of technologies used
│   │   └───source-tree.md      # This document: project directory structure
│   └───stories/                # User stories and epic breakdowns
├───logs/                       # Application and pipeline execution logs
├───models/
│   ├───hf_tokenizer/           # Hugging Face tokenizer files (if needed for future text processing)
│   ├───hf_model/               # Hugging Face model files (if needed for future text processing)
│   ├───label_encoder.joblib    # Saved LabelEncoder for target variable (if applicable)
│   ├───scaler.joblib           # Saved StandardScaler for numerical features (if applicable)
│   └───supervised_model.joblib # Saved supervised machine learning model (if applicable)
├───reports/
│   ├───pipeline/               # Reports generated by the data processing pipeline
│   └───quality/                # Quality reports for model performance
├───src/
│   ├───__init__.py             # Python package initializer
│   ├───auth/                   # Authentication related modules (if applicable)
│   ├───config/                 # Configuration files (e.g., config.py)
│   ├───data_processing/        # Modules for data cleaning, feature engineering, and pipeline orchestration
│   │   ├───feature_generator.py# Implements the logic for generating technical indicators and other market features
│   │   └───pipeline.py         # Orchestrates the entire data processing workflow
│   ├───backtesting/             # Contains the core backtesting engine
│   │   ├───__init__.py
│   │   ├───engine.py            # Core simulation logic
│   │   └───environment.py       # OpenAI Gym-like environment for RL agents
│   ├───agents/                  # Contains the RL agents (baseline, specialized, MoE)
│   │   ├───__init__.py
│   │   ├───base_agent.py        # Abstract base class for agents
│   │   ├───ppo_agent.py         # Implementation of the baseline PPO agent
│   │   └───moe_agent.py         # Mixture of Experts agent (future)
│   ├───models/                  # Contains neural network architectures
│   │   ├───__init__.py
│   │   └───lstm_model.py        # LSTM/GRU network for agents
│   ├───training/                # Contains training scripts and utilities
│   │   ├───__init__.py
│   │   └───trainer.py           # Script to manage agent training
│   └───utils/                   # General utilities (e.g., data loaders, metrics)
│       ├───__init__.py
│       ├───data_loader.py       # Loads data from data/final
│       └───metrics.py           # Performance metrics (Sharpe, Drawdown)
├───temp/                       # Temporary files or backups
├───web-bundles/                # Web-compatible bundles for agents (if applicable)
├───.gitignore                  # Specifies intentionally untracked files to ignore
├───data_processing_pipeline.log # Log file for data processing pipeline
├───DEVELOPER_TASK_PROCESS_SUMMARY.md # Summary of developer tasks
├───DEVELOPER_TASK_PROCESS.md   # Detailed developer task process
├───fyers_docs.txt              # Documentation related to Fyers API
├───read-data.ipynb             # Jupyter notebook for data exploration
├───requirements.txt            # Python dependencies
├───run_live_bot.py             # Main entry point for live trading
├───tests/                      # For unit and integration tests
│   ├───__init__.py
│   ├───test_backtesting.py
│   └───test_agents.py
```

## 1. Top-Level Directories

*   `.git/`: Standard Git repository directory.
*   `.gemini/`: Configuration and memory specific to the Gemini environment.
*   `.bmad-core/`: Contains the core definitions for the BMad-Method framework, including agents, tasks, templates, and checklists.
*   `data/`: Stores all data related to the project, categorized by processing stage.
*   `docs/`: Contains all project documentation, including architectural specifications, user stories, and high-level ideas.
*   `logs/`: Centralized location for all application and pipeline logs.
*   `models/`: Stores trained machine learning models, preprocessors, and Hugging Face model assets.
*   `reports/`: Contains various reports generated by the system, such as pipeline summaries and quality assessments.
*   `src/`: Contains the core source code of the trading bot, organized into logical modules.
*   `temp/`: A temporary directory for transient files or backups.
*   `web-bundles/`: Contains web-compatible bundles for agents, used in web UI environments.

## 2. Data Directory (`data/`)

*   `raw/`: For raw, untouched historical market data, typically in CSV format.
*   `final/`: For the final, cleaned, and enriched datasets ready for model training.

## 3. Docs Directory (`docs/`)

*   `mainIdea.md`: The overarching vision and high-level technical specification for the autonomous trading bot.
*   `architecture/`: Dedicated to architectural documentation.
    *   `coding-standards.md`: Defines the coding conventions and quality guidelines.
    *   `tech-stack.md`: Lists and describes the technologies and frameworks used.
    *   `source-tree.md`: This document, detailing the project's directory structure.
*   `stories/`: Contains individual user stories and epic breakdowns, guiding development tasks.

## 4. Models Directory (`models/`)

*   `hf_tokenizer/`: Stores the files for the Hugging Face tokenizer used for text processing (if needed for future text processing).
*   `hf_model/`: Stores the files for the Hugging Face model used for generating text embeddings (if needed for future text processing).
*   `label_encoder.joblib`: The serialized `LabelEncoder` object used for transforming target variables (if applicable).
*   `scaler.joblib`: The serialized `StandardScaler` object used for scaling numerical features (if applicable).
*   `supervised_model.joblib`: The serialized trained supervised machine learning model (if applicable).

## 5. Reports Directory (`reports/`)

*   `pipeline/`: Contains reports generated by the data processing pipeline, summarizing its execution and outcomes.
*   `quality/`: Stores reports related to the quality of trained models.

## 6. Source Directory (`src/`)

*   `__init__.py`: Marks `src` as a Python package.
*   `auth/`: (Optional) Modules related to authentication and authorization.
*   `config/`: Contains configuration files and settings for the application.
*   `data_processing/`: Core modules for handling data-related operations.
    *   `feature_generator.py`: Implements the logic for generating technical indicators and other market features.
    *   `pipeline.py`: Orchestrates the entire data processing workflow.
*   `backtesting/`: Contains the core backtesting engine.
    *   `__init__.py`
    *   `engine.py`: Core simulation logic.
    *   `environment.py`: OpenAI Gym-like environment for RL agents.
*   `agents/`: Contains the RL agents (baseline, specialized, MoE).
    *   `__init__.py`
    *   `base_agent.py`: Abstract base class for agents.
    *   `ppo_agent.py`: Implementation of the baseline PPO agent.
    *   `moe_agent.py`: Mixture of Experts agent (future).
*   `models/`: Contains neural network architectures.
    *   `__init__.py`
    *   `lstm_model.py`: LSTM/GRU network for agents.
*   `training/`: Contains training scripts and utilities.
    *   `__init__.py`
    *   `trainer.py`: Script to manage agent training.
*   `utils/`: General utilities (e.g., data loaders, metrics).
    *   `__init__.py`
    *   `data_loader.py`: Loads data from data/final.
    *   `metrics.py`: Performance metrics (Sharpe, Drawdown).

## 7. Root Level Files

*   `.gitignore`: Specifies files and directories that Git should ignore.
*   `data_processing_pipeline.log`: Log file for the main data processing pipeline.
*   `DEVELOPER_TASK_PROCESS_SUMMARY.md`: Summary of developer tasks.
*   `DEVELOPER_TASK_PROCESS.md`: Detailed developer task process.
*   `fyers_docs.txt`: Documentation related to the Fyers API.
*   `read-data.ipynb`: Jupyter notebook for initial data exploration and analysis.
*   `requirements.txt`: Lists all Python dependencies required for the project.
*   `run_live_bot.py`: Main entry point for live trading.
*   `tests/`: For unit and integration tests.
    *   `__init__.py`
    *   `test_backtesting.py`
    *   `test_agents.py`


## 1. Top-Level Directories

*   `.git/`: Standard Git repository directory.
*   `.gemini/`: Configuration and memory specific to the Gemini environment.
*   `.bmad-core/`: Contains the core definitions for the BMad-Method framework, including agents, tasks, templates, and checklists.
*   `data/`: Stores all data related to the project, categorized by processing stage.
*   `docs/`: Contains all project documentation, including architectural specifications, user stories, and high-level ideas.
*   `logs/`: Centralized location for all application and pipeline logs.
*   `models/`: Stores trained machine learning models, preprocessors, and Hugging Face model assets.
*   `reports/`: Contains various reports generated by the system, such as pipeline summaries and quality assessments.
*   `scripts/`: Houses standalone executable scripts for various operations (e.g., data processing, testing, utility functions).
*   `src/`: Contains the core source code of the trading bot, organized into logical modules.
*   `temp/`: A temporary directory for transient files or backups.
*   `web-bundles/`: Contains web-compatible bundles for agents, used in web UI environments.

## 2. Data Directory (`data/`)

*   `raw/`: For raw, untouched historical market data, typically in CSV format.
*   `processed/`: For intermediate data generated during the data processing pipeline, such as feature sets before reasoning is applied.
    *   `embeddings/`: Specifically for storing pre-computed numerical embeddings of text data (e.g., from the reasoning engine).
*   `final/`: For the final, cleaned, and enriched datasets ready for model training.

## 3. Docs Directory (`docs/`)

*   `mainIdea.md`: The overarching vision and high-level technical specification for the autonomous trading bot.
*   `architecture/`: Dedicated to architectural documentation.
    *   `coding-standards.md`: Defines the coding conventions and quality guidelines.
    *   `tech-stack.md`: Lists and describes the technologies and frameworks used.
    *   `source-tree.md`: This document, detailing the project's directory structure.
*   `stories/`: Contains individual user stories and epic breakdowns, guiding development tasks.

## 4. Models Directory (`models/`)

*   `hf_tokenizer/`: Stores the files for the Hugging Face tokenizer used for text processing.
*   `hf_model/`: Stores the files for the Hugging Face model used for generating text embeddings.
*   `label_encoder.joblib`: The serialized `LabelEncoder` object used for transforming target variables.
*   `scaler.joblib`: The serialized `StandardScaler` object used for scaling numerical features.
*   `supervised_model.joblib`: The serialized trained supervised machine learning model.

## 5. Reports Directory (`reports/`)

*   `pipeline/`: Contains reports generated by the data processing pipeline, summarizing its execution and outcomes.
*   `quality/`: Stores reports related to the quality of generated reasoning data and the performance of trained models.

## 6. Scripts Directory (`scripts/`)

*   `data_processing/`: Scripts specifically for data ingestion, transformation, or one-off data tasks.
*   `testing/`: Contains scripts for running various types of tests (unit, integration, end-to-end).
*   `organize_project.py`: A utility script for initial project setup, directory creation, or file organization.
*   `live_predictor.py`: (To be created) A script responsible for loading the trained model and making predictions on live market data.

## 7. Source Directory (`src/`)

*   `__init__.py`: Marks `src` as a Python package.
*   `auth/`: (Optional) Modules related to authentication and authorization.
*   `config/`: Contains configuration files and settings for the application.
*   `data_processing/`: Core modules for handling data-related operations.
    *   `feature_generator.py`: Implements the logic for generating technical indicators and other market features.
    *   `pipeline.py`: Orchestrates the entire data processing workflow.
    *   `reasoning_processor.py`: Integrates with the reasoning system to add human-like trading reasoning to data.
*   `reasoning_system/`: Contains the sophisticated AI reasoning engine.
    *   `core/`: Core logic, orchestrators, and main components of the reasoning system.
    *   `engines/`: Different reasoning engines or algorithms.
    *   `generators/`: Modules for generating natural language reasoning narratives.
    *   `managers/`: Manages the lifecycle and interaction of reasoning components.
    *   `templates/`: Templates used for structuring reasoning outputs.
    *   `validators/`: Modules for validating the quality and coherence of generated reasoning.
*   `supervised_model/`: Modules dedicated to the supervised machine learning models.
    *   `generate_embeddings.py`: Handles the generation of numerical embeddings from text data.
    *   `model_pipeline.py`: Manages the training, evaluation, and saving of supervised models.

## 8. Root Level Files

*   `.gitignore`: Specifies files and directories that Git should ignore.
*   `data_processing_pipeline.log`: Log file for the main data processing pipeline.
*   `DEVELOPER_TASK_PROCESS_SUMMARY.md`: Summary of developer tasks.
*   `DEVELOPER_TASK_PROCESS.md`: Detailed developer task process.
*   `fyers_docs.txt`: Documentation related to the Fyers API.
*   `read-data.ipynb`: Jupyter notebook for initial data exploration and analysis.
*   `reasoning_processor.log`: Log file for the reasoning processor.
*   `requirements.txt`: Lists all Python dependencies required for the project.
