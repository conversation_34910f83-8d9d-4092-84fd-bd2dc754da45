{"tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_act_with_memory": true, "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_think_and_predict": true, "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_statistics": true, "tests/test_autonomous_agent.py::TestAutonomousAgent::test_autonomous_agent_save_and_load": true, "tests/test_pattern_recognizer.py::TestPattern1DCNN::test_pattern_cnn_initialization": true, "tests/test_pattern_recognizer.py::TestPattern1DCNN::test_pattern_cnn_forward_pass": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_pattern_recognizer_initialization": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_prepare_price_data_numpy_array": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_prepare_price_data_pandas_dataframe": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_recognize_pattern_insufficient_data": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_recognize_pattern_hammer_scenario": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_get_pattern_features": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_get_pattern_features_insufficient_data": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_neural_network_integration": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_prepare_sequence_for_nn": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_calculate_technical_features": true, "tests/test_pattern_recognizer.py::TestPatternRecognizer::test_error_handling_invalid_data": true, "tests/test_self_modification.py::TestSelfModificationManager::test_check_performance_and_adapt_synaptic_pruning": true}