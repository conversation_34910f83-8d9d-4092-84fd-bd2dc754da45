#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.training.autonomous_trainer import run_autonomous_stage
from src.config.config import INITIAL_CAPITAL

def test_autonomous_training():
    print("Testing Autonomous Training System")
    print(f"Initial capital from config: {INITIAL_CAPITAL:,.2f}")
    
    # Test configuration
    config = {
        'autonomous': {
            'population_size': 2,
            'generations': 1,
            'episodes_per_evaluation': 1,
            'symbol': 'Nifty'
        },
        'symbol': 'Nifty',
        'initial_capital': INITIAL_CAPITAL
    }
    
    print("Starting autonomous training...")
    try:
        result = run_autonomous_stage(config)
        print(f"✅ Autonomous training completed successfully!")
        print(f"Generation: {result.get('generation', 'unknown')}")
        print(f"Best fitness: {result.get('best_fitness', 'unknown')}")
        print(f"Champion path: {result.get('champion_path', 'unknown')}")
        return True
    except Exception as e:
        print(f"❌ Autonomous training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_autonomous_training()
    if success:
        print("\n🎉 All tests passed! The autonomous system is working correctly.")
    else:
        print("\n💥 Tests failed. Please check the errors above.")
