{"cells": [{"cell_type": "code", "execution_count": 3, "id": "68f9c651", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>sma_5</th>\n", "      <th>ema_5</th>\n", "      <th>sma_10</th>\n", "      <th>ema_10</th>\n", "      <th>sma_20</th>\n", "      <th>ema_20</th>\n", "      <th>...</th>\n", "      <th>upper_shadow</th>\n", "      <th>lower_shadow</th>\n", "      <th>gap_up</th>\n", "      <th>gap_down</th>\n", "      <th>sma_5_20_cross</th>\n", "      <th>sma_10_50_cross</th>\n", "      <th>price_vs_sma_20</th>\n", "      <th>price_vs_ema_20</th>\n", "      <th>volatility_10</th>\n", "      <th>volatility_20</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>48577.05</td>\n", "      <td>48618.35</td>\n", "      <td>48560.35</td>\n", "      <td>48600.25</td>\n", "      <td>48571.71</td>\n", "      <td>48575.97</td>\n", "      <td>48554.06</td>\n", "      <td>48562.06</td>\n", "      <td>48537.63</td>\n", "      <td>48560.47</td>\n", "      <td>...</td>\n", "      <td>0.04</td>\n", "      <td>0.03</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0.13</td>\n", "      <td>0.08</td>\n", "      <td>0.07</td>\n", "      <td>0.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>48597.90</td>\n", "      <td>48615.40</td>\n", "      <td>48582.30</td>\n", "      <td>48606.85</td>\n", "      <td>48583.74</td>\n", "      <td>48586.26</td>\n", "      <td>48565.15</td>\n", "      <td>48570.21</td>\n", "      <td>48540.05</td>\n", "      <td>48564.89</td>\n", "      <td>...</td>\n", "      <td>0.02</td>\n", "      <td>0.03</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0.14</td>\n", "      <td>0.09</td>\n", "      <td>0.06</td>\n", "      <td>0.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>48606.35</td>\n", "      <td>48609.10</td>\n", "      <td>48586.25</td>\n", "      <td>48595.25</td>\n", "      <td>48590.04</td>\n", "      <td>48589.26</td>\n", "      <td>48574.12</td>\n", "      <td>48574.76</td>\n", "      <td>48542.28</td>\n", "      <td>48567.78</td>\n", "      <td>...</td>\n", "      <td>0.01</td>\n", "      <td>0.02</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0.11</td>\n", "      <td>0.06</td>\n", "      <td>0.04</td>\n", "      <td>0.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>48593.25</td>\n", "      <td>48595.25</td>\n", "      <td>48559.10</td>\n", "      <td>48581.75</td>\n", "      <td>48592.16</td>\n", "      <td>48586.75</td>\n", "      <td>48576.16</td>\n", "      <td>48576.03</td>\n", "      <td>48541.51</td>\n", "      <td>48569.11</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.05</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0.08</td>\n", "      <td>0.03</td>\n", "      <td>0.04</td>\n", "      <td>0.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>48580.65</td>\n", "      <td>48629.25</td>\n", "      <td>48577.85</td>\n", "      <td>48611.30</td>\n", "      <td>48599.08</td>\n", "      <td>48594.94</td>\n", "      <td>48582.46</td>\n", "      <td>48582.44</td>\n", "      <td>48544.73</td>\n", "      <td>48573.13</td>\n", "      <td>...</td>\n", "      <td>0.04</td>\n", "      <td>0.01</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0.14</td>\n", "      <td>0.08</td>\n", "      <td>0.04</td>\n", "      <td>0.10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 62 columns</p>\n", "</div>"], "text/plain": ["       open      high       low     close     sma_5     ema_5    sma_10  \\\n", "0  48577.05  48618.35  48560.35  48600.25  48571.71  48575.97  48554.06   \n", "1  48597.90  48615.40  48582.30  48606.85  48583.74  48586.26  48565.15   \n", "2  48606.35  48609.10  48586.25  48595.25  48590.04  48589.26  48574.12   \n", "3  48593.25  48595.25  48559.10  48581.75  48592.16  48586.75  48576.16   \n", "4  48580.65  48629.25  48577.85  48611.30  48599.08  48594.94  48582.46   \n", "\n", "     ema_10    sma_20    ema_20  ...  upper_shadow  lower_shadow  gap_up  \\\n", "0  48562.06  48537.63  48560.47  ...          0.04          0.03     0.0   \n", "1  48570.21  48540.05  48564.89  ...          0.02          0.03     0.0   \n", "2  48574.76  48542.28  48567.78  ...          0.01          0.02     0.0   \n", "3  48576.03  48541.51  48569.11  ...          0.00          0.05     0.0   \n", "4  48582.44  48544.73  48573.13  ...          0.04          0.01     0.0   \n", "\n", "   gap_down  sma_5_20_cross  sma_10_50_cross  price_vs_sma_20  \\\n", "0       0.0               1               -1             0.13   \n", "1       0.0               1               -1             0.14   \n", "2       0.0               1               -1             0.11   \n", "3       0.0               1               -1             0.08   \n", "4       0.0               1               -1             0.14   \n", "\n", "   price_vs_ema_20  volatility_10  volatility_20  \n", "0             0.08           0.07           0.09  \n", "1             0.09           0.06           0.09  \n", "2             0.06           0.04           0.10  \n", "3             0.03           0.04           0.09  \n", "4             0.08           0.04           0.10  \n", "\n", "[5 rows x 62 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "pd.read_csv('data/final/features_Bank_Nifty_5.csv').head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "0032b52f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}